2025-07-31 14:15:57 - Kaf<PERSON>WorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: 4cf0e8e9-620f-4961-95b5-38b22b6ff58e
2025-07-31 14:15:57 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/4cf0e8e9-620f-4961-95b5-38b22b6ff58e
2025-07-31 14:15:58 - WorkflowService - DEBUG - Received response with status code: 200
2025-07-31 14:15:58 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Untitled Workflow 6 retrieved successfully",
  "workflow": {
    "id": "4cf0e8e9-620f-4961-95b5-38b22b6ff58e",
    "name": "Untitled Workflow 6",
    "description": "Untitled_Workflow_6",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/72909a91-6321-4e00-a7c6-052b0373a7b7.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/40c7398f-ecae-4dd5-bbe3-0b1427d22a3a.json",
    "start_nodes": [
      {
        "field": "selector",
        "type": "string",
        "transition_id": "transition-SelectDataComponent-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-07-30T05:20:34.935084",
    "updated_at": "2025-07-31T08:38:54.709072",
    "available_nodes": [
      {
        "name": "SelectDataComponent",
        "display_name": "Select Data",
        "type": "component",
        "transition_id": "transition-SelectDataComponent-*************",
        "label": "Select Data"
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-1753945484349",
        "label": "Combine Text"
      }
    ],
    "is_updated": true,
    "source_version_id": null
  }
}
2025-07-31 14:16:01 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 4cf0e8e9-620f-4961-95b5-38b22b6ff58e - server_script_path is optional
2025-07-31 14:16:01 - app.services.initialize_workflow - DEBUG - Applied transition-specific value for field 'selector' to transition 'transition-SelectDataComponent-*************'
2025-07-31 14:16:01 - app.services.initialize_workflow - DEBUG - Calling _inject_user_input_for_conditional_components for transition-SelectDataComponent-*************
2025-07-31 14:16:01 - app.services.initialize_workflow - DEBUG - Checking transition transition-SelectDataComponent-*************: node_type='', is_conditional=False
2025-07-31 14:16:01 - app.services.initialize_workflow - DEBUG - Skipping non-conditional transition transition-SelectDataComponent-*************
2025-07-31 14:16:01 - app.services.initialize_workflow - DEBUG - Calling _inject_user_input_for_conditional_components for transition-LoopNode-*************
2025-07-31 14:16:01 - app.services.initialize_workflow - DEBUG - Checking transition transition-LoopNode-*************: node_type='', is_conditional=False
2025-07-31 14:16:01 - app.services.initialize_workflow - DEBUG - Skipping non-conditional transition transition-LoopNode-*************
2025-07-31 14:16:01 - app.services.initialize_workflow - DEBUG - Calling _inject_user_input_for_conditional_components for transition-CombineTextComponent-1753945484349
2025-07-31 14:16:01 - app.services.initialize_workflow - DEBUG - Checking transition transition-CombineTextComponent-1753945484349: node_type='', is_conditional=False
2025-07-31 14:16:01 - app.services.initialize_workflow - DEBUG - Skipping non-conditional transition transition-CombineTextComponent-1753945484349
2025-07-31 14:16:01 - app.services.initialize_workflow - DEBUG - Preserved payload structure in workflow: {'user_dependent_fields': ['selector'], 'user_payload_template': {'selector': {'value': 'a', 'transition_id': 'transition-SelectDataComponent-*************'}}}
2025-07-31 14:16:01 - EnhancedWorkflowEngine - DEBUG - Stored user_payload_template: {'selector': {'value': 'a', 'transition_id': 'transition-SelectDataComponent-*************'}}
2025-07-31 14:16:01 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-07-31 14:16:01 - StateManager - DEBUG - Using global database connections from initializer
2025-07-31 14:16:01 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-31 14:16:01 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-31 14:16:01 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-31 14:16:02 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-31 14:16:02 - StateManager - INFO - WorkflowStateManager initialized
2025-07-31 14:16:02 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-07-31 14:16:02 - StateManager - DEBUG - Using provided database connections
2025-07-31 14:16:02 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-31 14:16:02 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-31 14:16:02 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-31 14:16:03 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-31 14:16:03 - StateManager - INFO - WorkflowStateManager initialized
2025-07-31 14:16:03 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-SelectDataComponent-*************']
2025-07-31 14:16:03 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1753945484349: ['transition-LoopNode-*************']
2025-07-31 14:16:03 - StateManager - INFO - Built dependency map for 3 transitions
2025-07-31 14:16:03 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-SelectDataComponent-*************']
2025-07-31 14:16:03 - StateManager - DEBUG - Transition transition-CombineTextComponent-1753945484349 depends on: ['transition-LoopNode-*************']
2025-07-31 14:16:03 - EnhancedWorkflowEngine - DEBUG - Found end transition: transition-LoopNode-*************
2025-07-31 14:16:03 - EnhancedWorkflowEngine - INFO - Found 1 end transitions: {'transition-LoopNode-*************'}
2025-07-31 14:16:03 - StateManager - INFO - Set end transitions: {'transition-LoopNode-*************'}
2025-07-31 14:16:03 - MCPToolExecutor - DEBUG - Set correlation ID to: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850
2025-07-31 14:16:03 - EnhancedWorkflowEngine - DEBUG - Set correlation_id f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850 in tool_executor
2025-07-31 14:16:03 - NodeExecutor - DEBUG - Set correlation ID to: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850
2025-07-31 14:16:03 - EnhancedWorkflowEngine - DEBUG - Set correlation_id f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850 in node_executor
2025-07-31 14:16:03 - AgentExecutor - DEBUG - Set correlation ID to: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850
2025-07-31 14:16:03 - EnhancedWorkflowEngine - DEBUG - Set correlation_id f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850 in agent_executor
2025-07-31 14:16:03 - TransitionHandler - INFO - TransitionHandler initialized
2025-07-31 14:16:03 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850
2025-07-31 14:16:04 - KafkaWorkflowConsumer - DEBUG - Activity tracking initiated for workflow: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, activity_id: afc6144c-5b1f-4062-8939-039d590f0020_1753951564
2025-07-31 14:16:04 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850
2025-07-31 14:16:04 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, response: {'workflow_id': '4cf0e8e9-620f-4961-95b5-38b22b6ff58e', 'status': 'Initialized', 'message': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-07-31 14:16:04 - StateManager - INFO - Workflow initialized with initial transition: transition-SelectDataComponent-*************
2025-07-31 14:16:04 - StateManager - DEBUG - State: pending={'transition-SelectDataComponent-*************'}, waiting=set(), completed=set()
2025-07-31 14:16:04 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-SelectDataComponent-*************
2025-07-31 14:16:04 - StateManager - DEBUG - Workflow active: {'transition-SelectDataComponent-*************'}
2025-07-31 14:16:05 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850'
2025-07-31 14:16:05 - RedisManager - DEBUG - Set key 'workflow_state:f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850' with TTL of 600 seconds
2025-07-31 14:16:05 - StateManager - INFO - Workflow state saved to Redis for workflow ID: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850. Will be archived to PostgreSQL when Redis key expires.
2025-07-31 14:16:05 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-31 14:16:05 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-31 14:16:05 - StateManager - INFO - Cleared 1 pending transitions: {'transition-SelectDataComponent-*************'}
2025-07-31 14:16:05 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-31 14:16:05 - StateManager - INFO - Terminated: False
2025-07-31 14:16:05 - StateManager - INFO - Pending transitions (0): []
2025-07-31 14:16:05 - StateManager - INFO - Waiting transitions (0): []
2025-07-31 14:16:05 - StateManager - INFO - Completed transitions (0): []
2025-07-31 14:16:05 - StateManager - INFO - Results stored for 0 transitions
2025-07-31 14:16:05 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-31 14:16:05 - StateManager - INFO - Workflow status: inactive
2025-07-31 14:16:05 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-31 14:16:05 - StateManager - INFO - Workflow status: inactive
2025-07-31 14:16:05 - StateManager - INFO - Workflow paused: False
2025-07-31 14:16:05 - StateManager - INFO - ==============================
2025-07-31 14:16:05 - TransitionHandler - INFO - Starting parallel execution of transition: transition-SelectDataComponent-*************
2025-07-31 14:16:05 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850):
2025-07-31 14:16:05 - KafkaWorkflowConsumer - DEBUG - Activity log created for workflow: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, activity_id: afc6144c-5b1f-4062-8939-039d590f0020_1753951564, log_type: RESULT_LOG, log_status: LOG_STATUS_SUCCESS, activity_status: ACTIVITY_STATUS_IN_PROGRESS
2025-07-31 14:16:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, response: {'result': 'Starting execution of transition: transition-SelectDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-SelectDataComponent-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-07-31 14:16:05 - TransitionHandler - EXECUTE - Transition 'transition-SelectDataComponent-*************' (type=initial, execution_type=Components)
2025-07-31 14:16:05 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-31 14:16:05 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-SelectDataComponent-*************
2025-07-31 14:16:05 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-31 14:16:05 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-31 14:16:05 - TransitionHandler - DEBUG - 🔀 Conditional detection: node_type=component, is_conditional_node_type=False, is_conditional_transition_id=False
2025-07-31 14:16:05 - TransitionHandler - DEBUG - 🔧 PARALLEL DEBUG: input_data_configs for transition-SelectDataComponent-*************: []
2025-07-31 14:16:05 - TransitionHandler - WARNING - 🔧 PARALLEL DEBUG: Empty input_data_configs for transition-SelectDataComponent-*************
2025-07-31 14:16:05 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-31 14:16:05 - TransitionHandler - DEBUG - tool Parameters: {'input_data': {'a': '3'}, 'data_type': 'Auto-Detect', 'search_mode': 'Exact Path', 'field_matching_mode': 'Auto-detect', 'selector': 'a'}
2025-07-31 14:16:05 - TransitionHandler - INFO - Invoking tool 'SelectDataComponent' (tool_id: 1) for node 'SelectDataComponent' in transition 'transition-SelectDataComponent-*************' with parameters: {'input_data': {'a': '3'}, 'data_type': 'Auto-Detect', 'search_mode': 'Exact Path', 'field_matching_mode': 'Auto-detect', 'selector': 'a'}
2025-07-31 14:16:05 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850):
2025-07-31 14:16:05 - KafkaWorkflowConsumer - DEBUG - Activity log created for workflow: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, activity_id: afc6144c-5b1f-4062-8939-039d590f0020_1753951564, log_type: RESULT_LOG, log_status: LOG_STATUS_SUCCESS, activity_status: ACTIVITY_STATUS_IN_PROGRESS
2025-07-31 14:16:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, response: {'transition_id': 'transition-SelectDataComponent-*************', 'node_label': 'Select Data', 'tool_name': 'SelectDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server Select Data', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-07-31 14:16:05 - NodeExecutor - INFO - Executing tool 'SelectDataComponent' via Kafka (request_id: cef6a734-4cf1-4b74-8338-7e4832231267) using provided producer.
2025-07-31 14:16:05 - NodeExecutor - DEBUG - Added correlation_id f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850 to payload
2025-07-31 14:16:05 - NodeExecutor - DEBUG - Added transition_id transition-SelectDataComponent-************* to payload
2025-07-31 14:16:05 - NodeExecutor - DEBUG - Added node_label Select Data to payload
2025-07-31 14:16:05 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'SelectDataComponent', 'tool_parameters': {'input_data': {'a': '3'}, 'data_type': 'Auto-Detect', 'search_mode': 'Exact Path', 'field_matching_mode': 'Auto-detect', 'selector': 'a'}, 'request_id': 'cef6a734-4cf1-4b74-8338-7e4832231267', 'correlation_id': 'f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850', 'transition_id': 'transition-SelectDataComponent-*************', 'node_label': 'Select Data'}
2025-07-31 14:16:05 - NodeExecutor - DEBUG - Request cef6a734-4cf1-4b74-8338-7e4832231267 sent successfully using provided producer.
2025-07-31 14:16:05 - NodeExecutor - DEBUG - Waiting indefinitely for result for request cef6a734-4cf1-4b74-8338-7e4832231267...
2025-07-31 14:16:05 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1880, corr_id: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850
2025-07-31 14:16:06 - NodeExecutor - DEBUG - Result consumer received message: Offset=2199
2025-07-31 14:16:06 - NodeExecutor - DEBUG - Received valid result for request_id cef6a734-4cf1-4b74-8338-7e4832231267
2025-07-31 14:16:06 - NodeExecutor - INFO - Result received for request cef6a734-4cf1-4b74-8338-7e4832231267.
2025-07-31 14:16:06 - TransitionHandler - INFO - Execution result from Components executor: {
  "output_data": "3"
}
2025-07-31 14:16:06 - TransitionHandler - INFO - Checking execution result for errors: {
  "output_data": "3"
}
2025-07-31 14:16:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850):
2025-07-31 14:16:06 - KafkaWorkflowConsumer - DEBUG - Activity log created for workflow: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, activity_id: afc6144c-5b1f-4062-8939-039d590f0020_1753951564, log_type: RESULT_LOG, log_status: LOG_STATUS_SUCCESS, activity_status: ACTIVITY_STATUS_IN_PROGRESS
2025-07-31 14:16:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, response: {'transition_id': 'transition-SelectDataComponent-*************', 'node_label': 'Select Data', 'tool_name': 'SelectDataComponent', 'message': 'Transition Result received.', 'result': {'output_data': '3'}, 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'raw_result': {'output_data': '3'}, 'approval_required': False}
2025-07-31 14:16:06 - StateManager - DEBUG - Stored result for transition transition-SelectDataComponent-************* in memory: {'SelectDataComponent': {'transition_id': 'transition-SelectDataComponent-*************', 'node_label': 'Select Data', 'tool_name': 'SelectDataComponent', 'result': {'result': {'output_data': '3'}}, 'status': 'completed', 'timestamp': 1753951566.408793}}
2025-07-31 14:16:07 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-SelectDataComponent-*************'
2025-07-31 14:16:07 - RedisManager - DEBUG - Set key 'result:transition-SelectDataComponent-*************' with TTL of 300 seconds
2025-07-31 14:16:07 - StateManager - DEBUG - Stored result for transition transition-SelectDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-31 14:16:07 - StateManager - INFO - Marked transition transition-SelectDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-07-31 14:16:07 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-SelectDataComponent-*************'}
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-SelectDataComponent-*************
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'dict'>
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔀 Execution result keys: ['output_data']
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-SelectDataComponent-*************:
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-SelectDataComponent-*************, returning empty list
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-SelectDataComponent-*************
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-LoopNode-*************']
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-LoopNode-*************
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-LoopNode-*************']
2025-07-31 14:16:07 - TransitionHandler - INFO - Completed transition transition-SelectDataComponent-************* in 2.12 seconds
2025-07-31 14:16:07 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850):
2025-07-31 14:16:07 - KafkaWorkflowConsumer - DEBUG - Activity log created for workflow: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, activity_id: afc6144c-5b1f-4062-8939-039d590f0020_1753951564, log_type: TIME_LOG, log_status: LOG_STATUS_SUCCESS, activity_status: ACTIVITY_STATUS_IN_PROGRESS
2025-07-31 14:16:07 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, response: {'result': 'Completed transition in 2.12 seconds', 'message': 'Transition completed in 2.12 seconds', 'transition_id': 'transition-SelectDataComponent-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-SelectDataComponent-*************: ['transition-LoopNode-*************']
2025-07-31 14:16:07 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-31 14:16:07 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-LoopNode-*************']]
2025-07-31 14:16:07 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-SelectDataComponent-*************: ['transition-LoopNode-*************']
2025-07-31 14:16:07 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-31 14:16:07 - EnhancedWorkflowEngine - INFO - Transition transition-SelectDataComponent-************* completed successfully: 1 next transitions
2025-07-31 14:16:07 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-LoopNode-*************']
2025-07-31 14:16:07 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-LoopNode-*************']
2025-07-31 14:16:07 - EnhancedWorkflowEngine - INFO - Adding transition transition-LoopNode-************* to pending (all dependencies met)
2025-07-31 14:16:07 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-07-31 14:16:08 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850'
2025-07-31 14:16:08 - RedisManager - DEBUG - Set key 'workflow_state:f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850' with TTL of 600 seconds
2025-07-31 14:16:08 - StateManager - INFO - Workflow state saved to Redis for workflow ID: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850. Will be archived to PostgreSQL when Redis key expires.
2025-07-31 14:16:08 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-31 14:16:08 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-31 14:16:08 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-07-31 14:16:08 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-31 14:16:08 - StateManager - INFO - Terminated: False
2025-07-31 14:16:08 - StateManager - INFO - Pending transitions (0): []
2025-07-31 14:16:08 - StateManager - INFO - Waiting transitions (0): []
2025-07-31 14:16:08 - StateManager - INFO - Completed transitions (1): ['transition-SelectDataComponent-*************']
2025-07-31 14:16:08 - StateManager - INFO - Results stored for 1 transitions
2025-07-31 14:16:08 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-31 14:16:08 - StateManager - INFO - Workflow status: inactive
2025-07-31 14:16:08 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-31 14:16:08 - StateManager - INFO - Workflow status: inactive
2025-07-31 14:16:08 - StateManager - INFO - Workflow paused: False
2025-07-31 14:16:08 - StateManager - INFO - ==============================
2025-07-31 14:16:08 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-07-31 14:16:08 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850):
2025-07-31 14:16:08 - KafkaWorkflowConsumer - DEBUG - Activity log created for workflow: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, activity_id: afc6144c-5b1f-4062-8939-039d590f0020_1753951564, log_type: RESULT_LOG, log_status: LOG_STATUS_SUCCESS, activity_status: ACTIVITY_STATUS_IN_PROGRESS
2025-07-31 14:16:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-07-31 14:16:08 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=standard, execution_type=loop)
2025-07-31 14:16:08 - TransitionHandler - INFO - 🔍 LOOP STACK: Pushed loop transition-LoopNode-************* to stack. Stack size: 1
2025-07-31 14:16:08 - LoopExecutor - DEBUG - 🔗 Orchestration engine reference set in LoopExecutor
2025-07-31 14:16:08 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-07-31 14:16:08 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-07-31 14:16:08 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-07-31 14:16:08 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-07-31 14:16:08 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-07-31 14:16:08 - TransitionHandler - DEBUG - 🔀 Conditional detection: node_type=loop, is_conditional_node_type=False, is_conditional_transition_id=False
2025-07-31 14:16:08 - TransitionHandler - DEBUG - 🔧 PARALLEL DEBUG: input_data_configs for transition-LoopNode-*************: [{'from_transition_id': 'transition-SelectDataComponent-*************', 'source_node_id': 'Select Data', 'data_type': 'string', 'handle_mappings': [{'source_transition_id': 'transition-SelectDataComponent-*************', 'source_handle_id': 'output_data', 'target_handle_id': 'start', 'edge_id': 'reactflow__edge-SelectDataComponent-*************output_data-LoopNode-*************start'}]}]
2025-07-31 14:16:09 - StateManager - DEBUG - Retrieved result for transition transition-SelectDataComponent-************* from Redis
2025-07-31 14:16:09 - StateManager - DEBUG - Detected wrapped result structure for transition transition-SelectDataComponent-*************, extracting data
2025-07-31 14:16:09 - StateManager - DEBUG - Extracted double-nested result data for transition transition-SelectDataComponent-*************
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-SelectDataComponent-*************
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-SelectDataComponent-************* (total: 1)
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'output_data': {'result': {'output_data': '3'}}
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle output_data: {'output_data': '3'}
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - Found result.result: {'output_data': '3'} (type: <class 'dict'>)
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - Found handle 'output_data' directly in dict
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - Successfully extracted handle 'output_data' with path 'result.output_data': 3
2025-07-31 14:16:09 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-31 14:16:09 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Looking for results for transition transition-SelectDataComponent-*************, iteration_context: False
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Base ID transition-SelectDataComponent-************* results: found
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'output_data': {'result': {'output_data': '3'}}
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle output_data: {'output_data': '3'}
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - Found result.result: {'output_data': '3'} (type: <class 'dict'>)
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - Found handle 'output_data' directly in dict
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - Successfully extracted handle 'output_data' with path 'result.output_data': 3
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - ✅ Handle mapping success: output_data → start via path 'result.output_data': 3
2025-07-31 14:16:09 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-31 14:16:09 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-07-31 14:16:09 - WorkflowUtils - DEBUG - Filtering out field 'start' with null/empty value: None
2025-07-31 14:16:09 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 12 fields (2 null/empty fields removed)
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 📌 Added static parameter: source_type = number_range
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 📌 Added static parameter: batch_size = 1
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 📌 Added static parameter: end = 4
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 📌 Added static parameter: step = 1
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 📌 Added static parameter: parallel_execution = True
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 📌 Added static parameter: max_concurrent = 3
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 📌 Added static parameter: preserve_order = True
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 📌 Added static parameter: iteration_timeout = 60
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 📌 Added static parameter: aggregation_type = collect_all
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 📌 Added static parameter: include_metadata = True
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 📌 Added static parameter: on_iteration_error = continue
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 📌 Added static parameter: include_errors = True
2025-07-31 14:16:09 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'start': '3', 'source_type': 'number_range', 'batch_size': '1', 'end': '4', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-07-31 14:16:09 - TransitionHandler - DEBUG - tool Parameters: {'start': '3', 'source_type': 'number_range', 'batch_size': '1', 'end': '4', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-07-31 14:16:09 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'start': '3', 'source_type': 'number_range', 'batch_size': '1', 'end': '4', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-07-31 14:16:09 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850):
2025-07-31 14:16:09 - KafkaWorkflowConsumer - DEBUG - Activity log created for workflow: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, activity_id: afc6144c-5b1f-4062-8939-039d590f0020_1753951564, log_type: RESULT_LOG, log_status: LOG_STATUS_SUCCESS, activity_status: ACTIVITY_STATUS_IN_PROGRESS
2025-07-31 14:16:09 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, response: {'transition_id': 'transition-LoopNode-*************', 'node_label': 'For Each Loop', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server For Each Loop', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-07-31 14:16:09 - LoopExecutor - INFO - 🔄 Starting loop execution for transition transition-LoopNode-*************
2025-07-31 14:16:09 - LoopExecutor - INFO - 🔧 Resolving loop configuration for transition transition-LoopNode-*************
2025-07-31 14:16:09 - LoopExecutor - DEBUG - 🔧 STEP 0 - Initial loop_config: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 4}, 'step': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-1753945484349'], 'exit_transitions': ['transition-CombineTextComponent-1753945484349'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-07-31 14:16:09 - LoopExecutor - DEBUG - 🔧 STEP 0 - Initial resolved_config: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 4}, 'step': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-1753945484349'], 'exit_transitions': ['transition-CombineTextComponent-1753945484349'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-07-31 14:16:09 - LoopExecutor - DEBUG - 🔧 STEP 1 - tool_parameters: {'start': '3', 'source_type': 'number_range', 'batch_size': '1', 'end': '4', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-07-31 14:16:09 - LoopExecutor - DEBUG - 🔧 STEP 1 - tool_config: {}
2025-07-31 14:16:09 - LoopExecutor - DEBUG - 🔧 STEP 1 - resolved_config after tool_params: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 4}, 'step': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-1753945484349'], 'exit_transitions': ['transition-CombineTextComponent-1753945484349'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-07-31 14:16:09 - LoopExecutor - DEBUG - 🔧 STEP 2 - input_data_configs: [{'from_transition_id': 'transition-SelectDataComponent-*************', 'source_node_id': 'Select Data', 'data_type': 'string', 'handle_mappings': [{'source_transition_id': 'transition-SelectDataComponent-*************', 'source_handle_id': 'output_data', 'target_handle_id': 'start', 'edge_id': 'reactflow__edge-SelectDataComponent-*************output_data-LoopNode-*************start'}]}]
2025-07-31 14:16:09 - StateManager - DEBUG - Retrieved result for transition transition-SelectDataComponent-************* from Redis
2025-07-31 14:16:09 - StateManager - DEBUG - Detected wrapped result structure for transition transition-SelectDataComponent-*************, extracting data
2025-07-31 14:16:09 - StateManager - DEBUG - Extracted double-nested result data for transition transition-SelectDataComponent-*************
2025-07-31 14:16:09 - LoopExecutor - DEBUG - 🔧 STEP 2 - input_config (connected values): {}
2025-07-31 14:16:09 - LoopExecutor - DEBUG - 🔧 STEP 2 - resolved_config after connected values: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 4}, 'step': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-1753945484349'], 'exit_transitions': ['transition-CombineTextComponent-1753945484349'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-07-31 14:16:09 - LoopExecutor - DEBUG - 🔧 STEP 3 - resolved_config before defaults: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 4}, 'step': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-1753945484349'], 'exit_transitions': ['transition-CombineTextComponent-1753945484349'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-07-31 14:16:09 - LoopExecutor - DEBUG - 🔧 STEP 3 - resolved_config after defaults: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 4}, 'step': 1, 'source_type': 'number_range'}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-1753945484349'], 'exit_transitions': ['transition-CombineTextComponent-1753945484349'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-07-31 14:16:09 - LoopExecutor - DEBUG - 📋 FINAL - Resolved loop configuration: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 4}, 'step': 1, 'source_type': 'number_range'}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-1753945484349'], 'exit_transitions': ['transition-CombineTextComponent-1753945484349'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-07-31 14:16:09 - LoopStateManager - INFO - 🔧 Initializing loop state for loop loop_transition-LoopNode-*************_060b236a
2025-07-31 14:16:09 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_060b236a, transition_id: transition-LoopNode-*************
2025-07-31 14:16:09 - LoopStateManager - INFO - ✅ Loop state initialized: 4 iterations planned
2025-07-31 14:16:09 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-SelectDataComponent-*************']
2025-07-31 14:16:09 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1753945484349: ['transition-LoopNode-*************']
2025-07-31 14:16:09 - StateManager - INFO - Built dependency map for 3 transitions
2025-07-31 14:16:09 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-SelectDataComponent-*************']
2025-07-31 14:16:09 - StateManager - DEBUG - Transition transition-CombineTextComponent-1753945484349 depends on: ['transition-LoopNode-*************']
2025-07-31 14:16:09 - LoopStateManager - DEBUG - 🔍 Simple loop body detected: {'transition-CombineTextComponent-1753945484349'}
2025-07-31 14:16:09 - LoopStateManager - DEBUG - 🔍 Discovered loop body transitions: {'transition-CombineTextComponent-1753945484349'}
2025-07-31 14:16:09 - LoopStateManager - DEBUG - 🔗 Built loop body dependency map: {'transition-CombineTextComponent-1753945484349': []}
2025-07-31 14:16:09 - LoopStateManager - INFO - 🔧 Loop body configured - Entry: ['transition-CombineTextComponent-1753945484349'], Exit: ['transition-CombineTextComponent-1753945484349']
2025-07-31 14:16:09 - LoopStateManager - DEBUG - 🔗 Loop body dependencies: {'transition-CombineTextComponent-1753945484349': []}
2025-07-31 14:16:09 - LoopExecutor - INFO - 🔄 Executing iteration 1 with item: 1
2025-07-31 14:16:09 - LoopStateManager - INFO - 🚀 Starting iteration 1/4
2025-07-31 14:16:09 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_060b236a, transition_id: transition-LoopNode-*************
2025-07-31 14:16:09 - LoopExecutor - INFO - 🔍 LOOP BODY DEBUG: Entry transitions: ['transition-CombineTextComponent-1753945484349']
2025-07-31 14:16:09 - LoopExecutor - INFO - 🔍 LOOP BODY DEBUG: Exit transitions: ['transition-CombineTextComponent-1753945484349']
2025-07-31 14:16:09 - LoopStateManager - INFO - 🚀 Starting iteration 1/4
2025-07-31 14:16:09 - StateManager - DEBUG - Stored loop state for loop_id: loop_transition-LoopNode-*************_060b236a, transition_id: transition-LoopNode-*************
2025-07-31 14:16:09 - LoopExecutor - INFO - 🚀 Starting loop body execution with dependency-aware approach
2025-07-31 14:16:09 - LoopExecutor - INFO - 🔗 Loop body dependency map: {'transition-CombineTextComponent-1753945484349': []}
2025-07-31 14:16:09 - LoopExecutor - INFO - 🔍 LOOP BODY DEBUG: Executing batch with dependency validation: ['transition-CombineTextComponent-1753945484349']
2025-07-31 14:16:09 - LoopExecutor - INFO - 🔄 Executing transition: transition-CombineTextComponent-1753945484349
2025-07-31 14:16:09 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-1753945484349
2025-07-31 14:16:09 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850):
2025-07-31 14:16:09 - KafkaWorkflowConsumer - DEBUG - Activity log created for workflow: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, activity_id: afc6144c-5b1f-4062-8939-039d590f0020_1753951564, log_type: RESULT_LOG, log_status: LOG_STATUS_SUCCESS, activity_status: ACTIVITY_STATUS_IN_PROGRESS
2025-07-31 14:16:09 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f8577bfc-c8ec-4a91-96b3-2f1e2b2d75b8-1753951557043850, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-1753945484349', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-1753945484349', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-07-31 14:16:09 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-1753945484349' (type=standard, execution_type=Components)
2025-07-31 14:16:09 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-1753945484349
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 🔄 Found iteration context for loop execution: {'iteration_index': 0, 'iteration_item': 1, 'loop_id': 'loop_transition-LoopNode-*************_060b236a', 'transition_id': 'transition-LoopNode-*************'}
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 🔀 Conditional detection: node_type=component, is_conditional_node_type=False, is_conditional_transition_id=False
2025-07-31 14:16:09 - TransitionHandler - DEBUG - 🔧 PARALLEL DEBUG: input_data_configs for transition-CombineTextComponent-1753945484349: [{'from_transition_id': 'transition-LoopNode-*************', 'source_node_id': 'For Each Loop', 'data_type': 'string', 'handle_mappings': [{'source_transition_id': 'transition-LoopNode-*************', 'source_handle_id': 'current_item', 'target_handle_id': 'input_1', 'edge_id': 'reactflow__edge-LoopNode-*************current_item-CombineTextComponent-1753945484349input_1'}]}]
2025-07-31 14:16:10 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
